import z from "zod";
import { getNameSchema } from "./shared-schemas";
import { ItemStatusType } from "@/generated/prisma/client";

export const updateItemSchema = z.object({
	id: z.number(),
	name: getNameSchema(),
	description: z.string().optional(),
	price: z.number(),
	quantity: z.number(),
	unit: z.string().optional(),
	groceryTripId: z.number(),
	importId: z.string().optional(),
	itemTypes: z
		.array(z.object({ id: z.number() }))
		.min(1, "At least one item type is required"),
	percentConsumed: z.number(),
	status: z.nativeEnum(ItemStatusType),
});

export const createItemSchema = z.object({
	name: getNameSchema(),
	description: z.string().optional(),
	price: z.number(),
	quantity: z.number(),
	unit: z.string().optional(),
	groceryTripId: z.number(),
	importId: z.string().optional(),
	itemTypes: z
		.array(z.object({ id: z.number() }))
		.min(1, "At least one item type is required"),
	percentConsumed: z.number(),
	status: z.nativeEnum(ItemStatusType),
});

export type UpdateItemSchemaType = z.infer<typeof updateItemSchema>;
export type CreateItemSchemaType = z.infer<typeof createItemSchema>;
