import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { createItem, getFilteredItems, updateItem } from "@/types/item";

export const itemRouter = createTRPCRouter({
	getItems: protectedProcedure
		.input(getFilteredItems)
		.query(async ({ ctx, input }) => {
			const userId = ctx.session.user.id;
			if (!userId) {
				throw new TRPCError({ code: "UNAUTHORIZED" });
			}

			const { search, filters } = input;

			const items = await ctx.db.item.findMany({
				orderBy: [
					{
						status: "asc",
					},
				],
				where: {
					userId,
					itemTypes: {
						some: {
							name: {
								search: search
									? search?.trim().split(" ").join(" | ")
									: undefined,
							},
						},
					},
					status: filters?.length ? { in: filters } : undefined,
				},
				include: {
					itemTypes: {
						select: {
							id: true,
							name: true,
							storage_advice: true,
						},
					},
				},
			});

			return items;
		}),
	updateItem: protectedProcedure
		.input(updateItem)
		.mutation(async ({ ctx, input }) => {
			const userId = ctx.session.user.id;
			if (!userId) {
				throw new TRPCError({ code: "UNAUTHORIZED" });
			}
			const itemToUpdate = await ctx.db.item.findUnique({
				where: { id: input.id },
				include: { itemTypes: true },
			});

			if (!itemToUpdate) {
				throw new TRPCError({ code: "NOT_FOUND", message: "Item not found" });
			}

			const { itemTypes,  percentConsumed, status, ...rest } = input;

			const updatedItem = await ctx.db.item.update({
				where: { id: input.id },
				data: {
					...rest,
					status: percentConsumed === 100 ? 'EATEN' : status,
					itemTypes: {
						disconnect: itemToUpdate.itemTypes.map((types) => ({
							id: types.id,
						})),
						connect: itemTypes.map((types) => ({ id: types.id })),
					},
				},
			});

			if (!itemToUpdate) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Problem updating item and associated types",
				});
			}

			return updatedItem;
		}),
			createItem: protectedProcedure
		.input(createItem)
		.mutation(async ({ ctx, input }) => {
			const userId = ctx.session.user.id;
			if (!userId) {
				throw new TRPCError({ code: "UNAUTHORIZED" });
			}
			const { itemTypes, ...rest } = input;

			const createdItem = await ctx.db.item.create({
				data: {
					...rest,
        quantity: 1,
				percentConsumed: 0,
				status: "FRESH",
				userId,
        itemTypes: {
          connect: itemTypes.map((type) => ({ id: type.id })),
        },
         
				},
			});

			if (!createdItem) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Problem creating item and linking associated types",
				});
			}

			return createdItem;
		}),
});
