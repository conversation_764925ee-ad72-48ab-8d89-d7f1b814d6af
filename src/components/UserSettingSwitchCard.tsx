import { SwitchCard } from "@/components/SwitchCard";
import { useUserSetting } from "@/hooks/useUserSetting";
import { type PreferenceKey } from "@/types/user-preferences";

interface UserSettingSwitchCardProps {
  settingKey: PreferenceKey;
  id?: string;
  label?: string;
  description?: string;
}

export function UserSettingSwitchCard({
  settingKey,
  id,
  label,
  description,
}: UserSettingSwitchCardProps) {
  // Format the key for display if no label is provided
  const displayLabel = label || formatKeyToLabel(settingKey);

  // Format a default description if none is provided
  const displayDescription =
    description || `Enable or disable ${settingKey.toLowerCase()}.`;

  // Use the supplied id or the setting key
  const displayId = id || settingKey;

  const { value, setValue, isLoading, isUpdating } = useUserSetting(settingKey);

  if (settingKey === "emailFrequency") {
    return (
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Email Frequency</h3>
        <p className="text-muted-foreground text-sm">
          Choose how often you want to receive emails.
        </p>
        <div className="space-y-1">
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              name="emailFrequency"
              value="daily"
              checked={value === "daily"}
              onChange={() => setValue("daily")}
              className="h-4 w-4 border border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span>Daily</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              name="emailFrequency"
              value="weekly"
              checked={value === "weekly"}
              onChange={() => setValue("weekly")}
              className="h-4 w-4 border border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span>Weekly</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              name="emailFrequency"
              value="monthly"
              checked={value === "monthly"}
              onChange={() => setValue("monthly")}
              className="h-4 w-4 border border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span>Monthly</span>
          </label>
        </div>
      </div>
    );
  }

  return (
    <SwitchCard
      id={displayId}
      label={displayLabel}
      description={displayDescription}
      checked={value as boolean}
      onCheckedChange={setValue}
      disabled={isLoading || isUpdating}
    />
  );
}

// Helper function to format a key like "notifications" to "Notifications"
function formatKeyToLabel(key: string): string {
  return key.charAt(0).toUpperCase() + key.slice(1);
}
