import React from "react";
import {
  Pa<PERSON><PERSON>,
  Pa<PERSON>ationContent,
  Pagin<PERSON>Ellipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "./pagination";

export interface DataPaginationProps {
  /** Current page number (1-based) */
  currentPage: number;
  /** Total number of items */
  totalItems: number;
  /** Number of items per page */
  pageSize: number;
  /** Callback when page changes */
  onPageChange: (page: number) => void;
  /** Number of page links to show around current page (default: 1) */
  paginationWindow?: number;
  /** Whether to show pagination when total items <= pageSize (default: false) */
  showWhenSinglePage?: boolean;
  /** Custom className for the pagination container */
  className?: string;
}

export const DataPagination: React.FC<DataPaginationProps> = ({
  currentPage,
  totalItems,
  pageSize,
  onPageChange,
  paginationWindow = 1,
  showWhenSinglePage = false,
  className,
}) => {
  const totalPages = Math.ceil(totalItems / pageSize);

  // Don't render if there's only one page or less, unless explicitly requested
  if (!showWhenSinglePage && totalPages <= 1) {
    return null;
  }

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageClick = (page: number) => {
    onPageChange(page);
  };

  // Generate array of page numbers to display
  const getVisiblePages = () => {
    const pages: number[] = [];

    for (let i = 1; i <= totalPages; i++) {
      // Show page if it's within the window around current page
      if (
        i === currentPage ||
        i === currentPage - paginationWindow ||
        i === currentPage + paginationWindow
      ) {
        pages.push(i);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();
  console.log(visiblePages);
  const shouldShowEllipsis =
    currentPage > paginationWindow + 1 &&
    totalPages > currentPage + paginationWindow;

  return (
    <Pagination className={className}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            onClick={handlePrevious}
            aria-disabled={currentPage <= 1}
            className={
              currentPage <= 1
                ? "pointer-events-none opacity-50"
                : "cursor-pointer"
            }
          />
        </PaginationItem>

        {visiblePages.map((pageNumber) => (
          <PaginationItem key={pageNumber}>
            <PaginationLink
              onClick={() => handlePageClick(pageNumber)}
              isActive={currentPage === pageNumber}
              className="cursor-pointer"
            >
              {pageNumber}
            </PaginationLink>
          </PaginationItem>
        ))}

        {shouldShowEllipsis && (
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
        )}

        <PaginationItem>
          <PaginationNext
            onClick={handleNext}
            aria-disabled={currentPage >= totalPages}
            className={
              currentPage >= totalPages
                ? "pointer-events-none opacity-50"
                : "cursor-pointer"
            }
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};
