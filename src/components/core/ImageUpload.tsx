"use client";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import heicConvert from "heic-convert";
import { useRouter } from "next/navigation";
import pRetry from "p-retry";
import { PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { UploadButton, UploadDropzone } from "@/server/uploadthing/uploadthing";
import type { GroceryTrip } from "@/generated/prisma";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { processImageQueue } from "@/app/api/queue/processImageQueue";
import { useGroceryTrips } from "@/hooks/useGroceryTrips";
import Image from "next/image";

export const ImageUpload = ({
  groceryTripId,
  refetch,
  checkReceiptStatus,
  onSuccess,
  onError,
  style = "button",
}: {
  groceryTripId?: number;
  refetch?: () => void;
  checkReceiptStatus?: () => unknown;
  onSuccess?: () => void;
  onError?: () => void;
  style?: "button" | "dropzone" | "floating";
  label?: string;
}) => {
  const { createReceipt, createTrip } = useGroceryTrips();
  const router = useRouter();
  const user = useCurrentUser();

  const Component = (() => {
    switch (style) {
      case "dropzone":
        return UploadDropzone;
      case "floating":
      case "button":
        return UploadButton;
    }
  })();
  const getContent = () => {
    switch (style) {
      case "button":
        return (
          <Image
            src="/billing_icon.svg"
            alt="add new receipt"
            style={{
              width: "32rem",
              height: "32rem",
              fill: "white",
              marginLeft: "5px",
              zIndex: 999,
            }}
          />
        );
      case "dropzone":
        return <span>Upload</span>;
      case "floating":
        return (
          <div className="z-999 flex">
            <PlusCircle />
            <span className="ml-2">Add Receipt</span>
          </div>
        );
    }
  };

  return (
    <Component
      endpoint="imageUploader"
      content={{
        button: getContent(),
      }}
      appearance={{
        button({
          ready,
          isUploading,
        }: {
          ready: boolean;
          isUploading: boolean;
        }) {
          return cn(
            {
              "!bg-[#72b455]": style === "floating",
            },
            `custom-button ${ready ? "" : "custom-button-not-ready"} ${
              isUploading ? "custom-button-uploading" : ""
            }`,
          );
        },
        allowedContent: cn({
          "!hidden": style === "floating",
        }),
      }}
      onBeforeUploadBegin={async (input) => {
        const convertedFiles: File[] = [];

        for (const file of input) {
          if (file.type === "image/heic" || file.name.endsWith(".heic")) {
            try {
              const buffer = await file.arrayBuffer();
              const jpegBuffer = await heicConvert({
                // biome-ignore lint/suspicious/noExplicitAny: <explanation>
                buffer: Buffer.from(buffer) as any,
                format: "JPEG",
              });

              const jpgFile = new File(
                [jpegBuffer],
                file.name.replace(/\.heic$/i, ".jpg"),
                {
                  type: "image/jpeg",
                },
              );

              convertedFiles.push(jpgFile);
            } catch (error) {
              console.error("Error converting HEIC to JPEG:", error);
              onError?.();
              toast.error("There was a problem converting your image");
              break;
            }
          } else {
            convertedFiles.push(file);
          }
        }

        return convertedFiles;
      }}
      onClientUploadComplete={async (res) => {
        if (res.length < 1) return;
        let fallbackGroceryTrip: GroceryTrip | undefined = {
          id: 0,
        } as unknown as GroceryTrip;
        if (!groceryTripId) {
          fallbackGroceryTrip = await createTrip({
            name: `${dayjs().format("dddd")} grocery trip`,
          });
        }
        res?.forEach(async (image, index) => {
          const receipt = await createReceipt({
            url: image.url,
            groceryTripId: groceryTripId || fallbackGroceryTrip?.id || 0,
          });
          await processImageQueue({
            receiptId: receipt?.id || 0,
            url: image.ufsUrl,
          });
          if (res.length === index + 1) {
            await refetch?.();
            if (checkReceiptStatus)
              await pRetry(checkReceiptStatus, { factor: 3 });
            onSuccess?.();
          }
        });

        toast.success("File(s) uploaded!");
        if (style === "floating") {
          await router.push("#");
        }
      }}
      onUploadError={(error: Error) => {
        console.error("ERROR", error);
        onError?.();
        toast.error(error.message);
      }}
    />
  );
};
