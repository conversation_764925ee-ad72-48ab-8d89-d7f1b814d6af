"use client";
import dayjs from "dayjs";
import { SimpleDialog } from "../SimpleDialog";
import { ReceiptGallery } from "./ReceiptGallery";
import { ReceiptImportWizard } from "./ReceiptImportWizard";
import { ItemsTable } from "./ItemsTable";
interface TripDetailsDialogProps {
  trip: any;
  isOpen: boolean;
  onClose: () => void;
  refetchTrip: () => void;
}

export const TripDetailsDialog: React.FC<TripDetailsDialogProps> = ({
  trip,
  isOpen,
  onClose,
  refetchTrip,
}) => {
  if (!trip) return null;

  return (
    <SimpleDialog
      open={isOpen && !!trip}
      showCancel={false}
      onOpenChange={onClose}
      title={trip.name}
      size="lg"
      mobileView="bottom-drawer"
      description={dayjs(trip.createdAt).format("MMM DD")}
      classNames={{
        content: "pb-0 overflow-y-auto h-[calc(100vh-10rem)]",
      }}
    >
      <ReceiptGallery receipts={trip.receipts} />

      <div className="mt-6">
        <p className="text-md leading-none font-semibold">Import items</p>
        <div className="radius-round border-grey-200 my-3 mb-12 flex max-w-[630px] flex-col gap-3 rounded-sm border-2 p-2">
          {trip.receipts[0] && (
            <ReceiptImportWizard
              receipt={trip.receipts[0]}
              refetchTrip={refetchTrip}
            />
          )}
        </div>
      </div>

      <div className="overflow-y-auto overflow-y-hidden">
        <ItemsTable items={trip.items} onCancel={onClose} />
      </div>
    </SimpleDialog>
  );
};
