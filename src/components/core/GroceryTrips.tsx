"use client";
import { Card, CardContent } from "@/components/ui/card";
import { useItemTypes } from "@/hooks/useItemTypes";
import { Spinner } from "../Spinner";
import { useEffect, useState } from "react";
import { useGroceryTrips } from "@/hooks/useGroceryTrips";
import localizedFormat from "dayjs/plugin/localizedFormat";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import dayjs from "dayjs";
import { Button } from "../ui/button";
import type { ItemType, Item } from "@/generated/prisma";
import { SimpleDialog } from "../SimpleDialog";
import {
  type ColumnDef,
  useReactTable,
  getCoreRowModel,
  flexRender,
} from "@tanstack/react-table";
import { Input } from "../Input";
import { useItems } from "@/hooks/useItems";
import { InputSelectTrigger, SearchableSelect } from "../SearchableSelect";
import Image from "next/image";
import { ReceiptImportWizard } from "./ReceiptImportWizard";
import page from "@/app/env-client/page";
import { DataPagination } from "../ui/DataPagination";
dayjs.extend(localizedFormat);

dayjs().format("L LT");

const shimmer = (w: number, h: number) => `
<svg width="${w}" height="${h}" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <linearGradient id="g">
      <stop stop-color="#333" offset="20%" />
      <stop stop-color="#222" offset="50%" />
      <stop stop-color="#333" offset="70%" />
    </linearGradient>
  </defs>
  <rect width="${w}" height="${h}" fill="#333" />
  <rect id="r" width="${w}" height="${h}" fill="url(#g)" />
  <animate xlink:href="#r" attributeName="x" from="-${w}" to="${w}" dur="1s" repeatCount="indefinite"  />
</svg>`;

const toBase64 = (str: string) =>
  typeof window === "undefined"
    ? Buffer.from(str).toString("base64")
    : window.btoa(str);

export const ItemCategoryColors: Record<string, string> = {
  Vegetables: "#4CAF50",
  Fruit: "#FF9800",
  Other: "#f5deb3",
  Proteins: "#8D6E63",
  "Non-Dairy Milk": "#6495ed",
  Meat: "#F44336",
  Eggs: "#FFF59D",
  Test: "#2196F3",
  Frozen: "#90CAF9",
  Pantry: "#FFCC80",
  Mushrooms: "#795548",
  Dairy: "#FCE4EC",
  Herbs: "#66BB6A",
};

export const GroceryTrips = () => {
  const { trips, isLoading, refetch } = useGroceryTrips();
  const [isTripOpen, setIsTripOpen] = useState(false);
  const [tripToUpdateIndex, setTripToUpdateIndex] = useState<
    number | undefined
  >();
  const [page, setPage] = useState(1);
  const PAGE_SIZE = 8;
  const PAGINATION_WINDOW = 3;

  return (
    <>
      <Card className="col-span-4 p-6 md:col-span-3 md:col-start-5 md:row-start-2">
        <h2 className="mb-4 text-xl font-semibold">Recent Trips</h2>
        {isLoading ? (
          <Spinner size="md" className="h center my-8" />
        ) : (
          <CardContent className="flex-1 px-0 pb-0">
            {trips.length ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {trips.map((trip, index) => (
                      <TableRow key={trip.id}>
                        <TableCell className="font-medium">
                          {trip.name}
                        </TableCell>
                        <TableCell>
                          {dayjs(trip.createdAt).format("ll")}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setTripToUpdateIndex(index);
                              setIsTripOpen(true);
                            }}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <div className="mt-2">
                  <DataPagination
                    currentPage={page}
                    totalItems={trips.length}
                    pageSize={PAGE_SIZE}
                    onPageChange={setPage}
                    paginationWindow={PAGINATION_WINDOW}
                  />
                </div>
              </>
            ) : (
              <p>Add a receipt to get started!</p>
            )}
          </CardContent>
        )}
      </Card>
      {isTripOpen &&
        tripToUpdateIndex !== undefined &&
        trips[tripToUpdateIndex] && (
          <SimpleDialog
            open={isTripOpen && !!trips[tripToUpdateIndex]}
            showCancel={false}
            onOpenChange={() => setIsTripOpen(false)}
            title={trips[tripToUpdateIndex].name}
            size="lg"
            mobileView="bottom-drawer"
            description={dayjs(trips[tripToUpdateIndex].createdAt).format(
              "MMM DD",
            )}
            classNames={{
              content: "pb-0 overflow-y-auto h-[calc(100vh-10rem)]",
            }}
          >
            <div className="mb-4 flex flex-col gap-2">
              <p className="text-md leading-none font-semibold">Receipts</p>
              <div className="flex gap-3">
                {trips[tripToUpdateIndex].receipts.map((receipt, index) => (
                  <Image
                    src={receipt.url}
                    alt={`receipt ${index}`}
                    key={receipt.id}
                    className="h-15 w-15 rounded-full"
                    width={15}
                    height={15}
                    placeholder={`data:image/svg+xml;base64,${toBase64(shimmer(700, 475))}`}
                  />
                ))}
              </div>
            </div>
            <div className="mt-6">
              <p className="text-md leading-none font-semibold">Import items</p>
              <div className="radius-round border-grey-200 my-3 mb-12 flex max-w-[630px] flex-col gap-3 rounded-sm border-2 p-2">
                {trips[tripToUpdateIndex].receipts[0] && (
                  <ReceiptImportWizard
                    receipt={trips[tripToUpdateIndex].receipts[0]}
                    refetchTrip={refetch}
                  />
                )}
              </div>
            </div>
            <div className="overflow-y-auto overflow-y-hidden">
              <ItemsTable
                items={trips[tripToUpdateIndex].items as unknown as Item[]}
                onCancel={() => setIsTripOpen(false)}
              />
            </div>
          </SimpleDialog>
        )}
    </>
  );
};

const ItemsTable = ({
  items,
  onCancel,
}: {
  items: (Item & { itemTypes: Pick<ItemType, "id" | "name">[] })[];
  onCancel: () => void;
}) => {
  const [itemState, setItemState] = useState<
    Record<number, Item & { itemTypes: string }>
  >({});
  const { types: itemTypes } = useItemTypes();
  const { updateItem } = useItems();

  const columns: ColumnDef<Item>[] = [
    {
      accessorKey: "name",
      header: "Name",
      size: 5,
      cell: ({ row, table, row: { getValue }, column: { id: columnId } }) => {
        const initialValue =
          itemState[row.original.id]?.name || getValue("name");
        const [value, setValue] = useState(initialValue);
        const onBlur = () => {
          table.options.meta?.updateData(columnId, row.original, value);
        };
        useEffect(() => {
          setValue(initialValue);
        }, [initialValue]);
        return (
          <Input
            name="name"
            type="text"
            placeholder="Name"
            onChange={(e) => setValue(e.target.value)}
            onBlur={onBlur}
            defaultValue={initialValue as string}
            value={value as string}
          />
        );
      },
    },
    {
      accessorKey: "price",
      header: "Price",
      cell: ({ row, table, row: { getValue }, column: { id: columnId } }) => {
        const initialValue = Number.parseFloat(
          (itemState[row.original.id]?.price as unknown as string) ||
            getValue("price"),
        ).toFixed(2);
        const [value, setValue] = useState(initialValue);
        const onBlur = () => {
          table.options.meta?.updateData(columnId, row.original, value);
        };
        useEffect(() => {
          setValue(initialValue);
        }, [initialValue]);
        return (
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span className="text-muted-foreground">$</span>
            </div>

            <Input
              className={"pl-9"}
              name="price"
              type="number"
              min={0}
              max={10000}
              step={0.01}
              placeholder="0.00"
              defaultValue={initialValue}
              onChange={(e) => setValue(e.target.value)}
              onBlur={onBlur}
              value={value}
            />
          </div>
        );
      },
    },
    {
      accessorKey: "itemTypes",
      header: "Item Type",
      cell: ({ row, table, row: { getValue }, column: { id: columnId } }) => {
        const initialValue = (getValue("itemTypes") as ItemType[]) || [];
        const initialValueId = initialValue[0]?.id;
        const startingValue =
          itemState?.[row.original.id]?.itemTypes || initialValueId;
        const [value, setValue] = useState(startingValue);
        const updateTableState = (val: string) => {
          table.options.meta?.updateData(columnId, row.original, val);
        };
        useEffect(() => {
          setValue(`${startingValue}`);
        }, [startingValue]);

        return (
          <SearchableSelect
            options={itemTypes.map((type) => ({
              label: type.name,
              value: type.id.toString(),
            }))}
            value={`${value}`}
            onValueChange={(val) => {
              setValue(val);
              updateTableState(val);
            }}
            placeholder="Select item type"
          >
            {(provided) => <InputSelectTrigger {...provided} />}
          </SearchableSelect>
        );
      },
    },
  ];

  const table = useReactTable({
    data: items,
    columns,
    getCoreRowModel: getCoreRowModel(),
    meta: {
      updateData: (columnId, original, value) => {
        setItemState((state) => {
          const currentOptions =
            state[original.id] ??
            ({} as Item & {
              itemTypes: string;
            });
          const originalItemTypes = original?.itemTypes?.[0]?.id;
          return {
            ...state,
            [original.id]: {
              ...original,
              ...currentOptions,
              itemTypes: currentOptions.itemTypes || originalItemTypes,
              [columnId]: value,
            },
          };
        });
      },
    },
  });

  const onSave = () => {
    for (const update of Object.values(itemState)) {
      const itemUpdate = {
        ...update,
        price: Number.parseFloat(`${update.price}`),
        itemTypes: [{ id: Number.parseInt(`${update.itemTypes}`) }],
      };
      updateItem(itemUpdate);
    }
    onCancel();
  };

  return (
    <>
      {" "}
      <p className="text-md mb-3 leading-none font-semibold">Items</p>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="mt-6 flex justify-end gap-3">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={onSave}>Save and Close</Button>
      </div>
    </>
  );
};
