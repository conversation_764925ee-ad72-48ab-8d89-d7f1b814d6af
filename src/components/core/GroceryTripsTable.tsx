"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Button } from "../ui/button";
import { DataPagination } from "../ui/DataPagination";
import dayjs from "dayjs";
import { type GroceryTrip } from "@/generated/prisma";
interface GroceryTripsTableProps {
  trips: unknown[];
  currentPage: number;
  pageSize: number;
  paginationWindow: number;
  onPageChange: (page: number) => void;
  onTripSelect: (index: number) => void;
}

export const GroceryTripsTable: React.FC<GroceryTripsTableProps> = ({
  trips,
  currentPage,
  pageSize,
  paginationWindow,
  onPageChange,
  onTripSelect,
}) => {
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const _trips = trips as GroceryTrip[];
  const paginatedTrips = _trips.slice(startIndex, endIndex);

  if (!trips.length) {
    return <p>Add a receipt to get started!</p>;
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {paginatedTrips.map((trip, paginatedIndex) => {
            const originalIndex = startIndex + paginatedIndex;
            return (
              <TableRow key={trip.id}>
                <TableCell className="font-medium">{trip.name}</TableCell>
                <TableCell>{dayjs(trip.createdAt).format("ll")}</TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onTripSelect(originalIndex)}
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
      <div className="mt-2">
        <DataPagination
          currentPage={currentPage}
          totalItems={trips.length}
          pageSize={pageSize}
          onPageChange={onPageChange}
          paginationWindow={paginationWindow}
        />
      </div>
    </>
  );
};
