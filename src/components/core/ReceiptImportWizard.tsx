import { use, useEffect, useState } from "react";
import { type CombinedItemType } from "./Inventory";
import { Item, type Receipt } from "@/generated/prisma";
import { ItemForm } from "./ItemForm";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "../ui/carousel";
import { useItemTypes } from "@/hooks/useItemTypes";
import { useItems } from "@/hooks/useItems";
import { type UpdateItemSchemaType } from "@/schemas/update-item";
import { useGroceryTrips } from "@/hooks/useGroceryTrips";
import { queueItemUpdates } from "@/utils";

type UpdateItemProps = {
  importId?: string | undefined;
  unit: string;
  id: number;
  status: "BAD" | "OLD" | "FRESH" | "EATEN" | "DISCARDED";
  name: string;
  itemTypes: string[];
  groceryTripId: string;
  description: string;
  price: number;
  quantity: number;
  percentConsumed: number;
};

type ImportedItemProps = {
  name?: string;
  price?: number;
  quantity?: number;
  importId: string;
  unit?: string;
  itemTypes?: {
    id: number;
    name: string;
  }[];
  category?: string;
};

export const ReceiptImportWizard = ({
  receipt,
  refetchTrip,
}: {
  receipt: Receipt & { items: Item[] };
  refetchTrip: () => void;
}) => {
  const { types: itemTypes } = useItemTypes();
  const [itemIndex, setItemIndex] = useState(0);
  const [importedData, setImportedData] = useState<
    (CombinedItemType | ImportedItemProps)[]
  >([]);
  const [activeItem, setActiveItem] = useState<
    CombinedItemType | ImportedItemProps | undefined
  >();
  const { createItem } = useItems();
  const { updateReceipt } = useGroceryTrips();

  const nextStep = () => {
    const nextIndex = itemIndex + 1;
    if (nextIndex <= importedData.length - 1) {
      setItemIndex(nextIndex);
      setActiveItem(importedData[nextIndex]);
    } else {
    }
  };
  const prevStep = () => {
    const previousIndex = itemIndex - 1;
    if (itemIndex !== 0) {
      setItemIndex(previousIndex);
      setActiveItem(importedData[previousIndex]);
    }
  };
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const scrapedData = JSON.parse(receipt.scrapedData as string);
    if (scrapedData && "items" in scrapedData) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      const mergedData = (scrapedData.items as ImportedItemProps[])
        .map((data) => {
          const foundSubmittedItem = receipt?.items?.find(
            (item) => item.importId === data?.importId,
          );
          const itemTypeFound = itemTypes?.find(
            (item) => item.name === data.category,
          );
          console.log({ foundSubmittedItem });
          return (
            !foundSubmittedItem && {
              ...data,
              price: Number.parseFloat(
                data?.price?.toString().replaceAll("$", "") || "0",
              ),
              itemTypes: itemTypeFound ? [itemTypeFound] : [],
            }
          );
        })
        .filter(Boolean);
      setImportedData(mergedData);
      setActiveItem(mergedData[itemIndex]);
    }
  }, [receipt, itemIndex, itemTypes]);
  const isItemSaved = !!receipt?.items?.find(
    (item) => item.importId === activeItem?.importId,
  );

  const handleNewItemSave = async (values: UpdateItemSchemaType) => {
    if (!receipt.groceryTripId) return;
    try {
      const item = await createItem({
        ...values,
        groceryTripId: receipt.groceryTripId,
        quantity: 1,
        percentConsumed: 0,
        status: "FRESH",
      });
      refetchTrip();
      const itemType = itemTypes.find(
        (item) => item.id === Number.parseInt(`${values.itemTypes[0]?.id}`),
      );
      await updateReceipt({
        id: receipt.id,
        itemId: item.id,
      });
      await queueItemUpdates(item, itemType);

      nextStep();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Carousel className="mx-auto w-[85%]">
      <CarouselContent>
        {Object.values(importedData).map((activeItem, index) => (
          <CarouselItem key={index}>
            <div className="p-1">
              <ItemForm
                defaultValues={activeItem}
                onCancel={() => undefined}
                onSubmit={(val) => handleNewItemSave(val)}
                isImport
              />
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
};
